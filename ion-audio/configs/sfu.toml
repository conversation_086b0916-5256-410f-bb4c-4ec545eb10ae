[global]
pprof = ":6062"
# data center id
dc = "dc1"
# internet ip
addr = "127.0.0.1"

[plugins]
on = true

[router]
# pass bandwidth feeback to pub
rembfeedback = false
# Cap bandwidth feedback
minbandwidth = 100000
maxbandwidth = 5000000
liveCycle = 6

[plugins.jitterbuffer]
on = true
# transport-cc (experiment feature)
tccon = false
# the remb cycle sending to pub, this told the pub it's bandwidth
rembcycle = 2
# pli cycle sending to pub, and pub will send a key frame
plicycle = 1
# this limit the remb bandwidth
maxbandwidth = 1000
# max buffer time by ms
maxbuffertime = 1000

[plugins.rtpforwarder]
on = false
# remote address
addr = "127.0.0.1:6668"
# kcp key
kcpkey = ""
# kcp salt
kcpsalt = ""

[webrtc]

# Range of ports that ion accepts WebRTC traffic on
# Format: [min, max]   and max - min >= 100
# portrange = [50000, 60000]
# if sfu behind nat, set iceserver
# [[webrtc.iceserver]]
# urls = ["stun:stun.stunprotocol.org:3478"]
# [[webrtc.iceserver]]
# urls = ["turn:turn.awsome.org:3478"]
# username = "awsome"
# credential = "awsome"
[rtp]
# listen port
port = 6666

# kcpkey = ""
# kcpsalt = ""
[log]
level = "info"

# level = "debug"
[etcd]
# ["ip:port", "ip:port"]
addrs = ["127.0.0.1:2389"]
grantTimeout = 5

[nats]
url = "nats://127.0.0.1:4223"
