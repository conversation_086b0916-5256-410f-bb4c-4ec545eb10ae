[global]
pprof = ":6064"

# internet ip
addr = "127.0.0.1"

# data center id
dc = "dc1"

[log]
level = "info"
# level = "debug"

[etcd]
# ["ip:port", "ip:port"]
addrs = ["127.0.0.1:2389"]
grantTimeout = 5

[signal]
#listen ip port
host = "0.0.0.0"
port = "8443"
# cert= "configs/cert.pem"
# key= "configs/key.pem"
path = "/ws"
authorization = true
allow_disconnected = false
allow_redis_cache = false

[nats]
url = "nats://127.0.0.1:4223"

[database]
# ["ip:port", "ip:port"]
# name  = "sqlite3"
# addrs = "/Users/<USER>/develop/project/ion-http-server/gorm.db"
name  = "mysql"
addrs = "root:password@tcp(localhost:3306)/fangyu?parseTime=true&collation=utf8mb4_unicode_ci&loc=Local"
# addrs = "root@tcp(*************:33333)/ion?charset=utf8&parseTime=True&loc=Local"
uidkey = "la6YsO+bNX/+XIkOqc5Svw=="

[auth]
secretKey = "jcBUVtmuhdQ+X9i1T1O/gTJm9fsogxGq"

[redis]
addrs = [":6380"]
password = ""
db = 0

[room]
pmi = 10
ordinary = 9

[domain]
name = "http://*************:5000"

[tinode]
addr = "localhost:16060"