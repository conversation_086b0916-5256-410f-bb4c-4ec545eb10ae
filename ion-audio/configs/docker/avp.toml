[global]
pprof = ":6063"
# data center id
dc = "dc1"
# internet ip
addr = "127.0.0.1"

[pipeline.samplebuilder]
# max late for audio rtp packets
audiomaxlate = 100
# max late for video rtp packets
videomaxlate = 200

[pipeline.webmsaver]
enabled = true
togglable = true
defaulton = true
# webm output path
path = "./out/"

[rtp]
# listen port
port = 6668
kcpkey = ""
kcpsalt = ""

[log]
level = "info"

# level = "debug"
[etcd]
# ["ip:port", "ip:port"]
addrs = ["etcd:2379"]
grantTimeout = 5

[nats]
url = "nats://nats:4222"
