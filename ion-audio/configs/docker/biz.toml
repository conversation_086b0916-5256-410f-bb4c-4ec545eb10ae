[global]
pprof = ":6060"

# internet ip
addr = "127.0.0.1"

# data center id
dc = "dc1"

[log]
level = "info"
# level = "debug"

[etcd]
# ["ip:port", "ip:port"]
addrs = ["etcd:2379"]
grantTimeout = 5

[signal]
#listen ip port
host = "0.0.0.0"
port = "8443"
# cert= "/configs/certs/cert.pem"
# key= "/configs/certs/key.pem"
path = "/ws"
authorization = true
allow_disconnected = false
allow_redis_cache = false

[nats]
url = "nats://nats:4222"

[database]
# ["ip:port", "ip:port"]
# name  = "sqlite3"
# addrs = "/Users/<USER>/develop/project/ion-http-server/gorm.db"
name  = "mysql"
addrs = "root:password@tcp(host.docker.internal:3306)/encryptDB?charset=utf8&parseTime=True&loc=Local"
uidkey = "la6YsO+bNX/+XIkOqc5Svw=="

[auth]
secretKey = "jcBUVtmuhdQ+X9i1T1O/gTJm9fsogxGq"

[redis]
addrs = ["redis:6379"]
password = ""
db = 0

[room]
pmi = 10
ordinary = 9

[domain]
name = "http://*************:5000"

[tinode]
addr = "tinode:16060"