[global]
pprof = ":6062"

# data center id
dc = "dc1"

# internet ip
addr = "127.0.0.1"

[router]
# pass bandwidth feeback to pub
rembfeedback = false
# Cap bandwidth feedback
minbandwidth = 100000
maxbandwidth = 5000000
liveCycle = 18

[plugins]
on = true

[plugins.jitterbuffer]
on = true
# transport-cc (experiment feature)
tccon = false
# the remb cycle sending to pub, this told the pub it's bandwidth
rembcycle = 2
# pli cycle sending to pub, and pub will send a key frame
plicycle = 1
# this limit the remb bandwidth
maxbandwidth = 1000
# max buffer time by ms
maxbuffertime = 1000

[plugins.rtpforwarder]
on = false
# remote address
addr = "avp:6668"
# kcp key
kcpkey = ""
# kcp salt
kcpsalt = ""

[webrtc]
# Range of ports that ion accepts WebRTC traffic on
portrange = [5000, 5200]
[webrtc.iceserver]
urls = ["stun:stun.l.google.com:19302"]

[rtp]
# listen port
port = 6666
# kcpkey = ""
# kcpsalt = ""

[log]
level = "info"
# level = "debug"

[etcd]
# ["ip:port", "ip:port"]
addrs = ["etcd:2379"]
grantTimeout = 30

[nats]
url = "nats://nats:4222"
